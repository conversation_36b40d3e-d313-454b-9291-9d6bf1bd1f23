{"version": 4, "terraform_version": "1.5.7", "serial": 4, "lineage": "4a1d2225-c3d1-02ad-0fd8-73c92ee634be", "outputs": {}, "resources": [{"mode": "managed", "type": "aws_dynamodb_table", "name": "terraform_locks", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:dynamodb:us-east-1:730933719998:table/my-eks-terraform-locks", "attribute": [{"name": "LockID", "type": "S"}], "billing_mode": "PAY_PER_REQUEST", "deletion_protection_enabled": false, "global_secondary_index": [], "hash_key": "LockID", "id": "my-eks-terraform-locks", "import_table": [], "local_secondary_index": [], "name": "my-eks-terraform-locks", "on_demand_throughput": [], "point_in_time_recovery": [{"enabled": false, "recovery_period_in_days": 0}], "range_key": null, "read_capacity": 0, "region": "us-east-1", "replica": [], "restore_date_time": null, "restore_source_name": null, "restore_source_table_arn": null, "restore_to_latest_time": null, "server_side_encryption": [], "stream_arn": "", "stream_enabled": false, "stream_label": "", "stream_view_type": "", "table_class": "STANDARD", "tags": null, "tags_all": {}, "timeouts": null, "ttl": [{"attribute_name": "", "enabled": false}], "write_capacity": 0}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjYwMDAwMDAwMDAwMCwidXBkYXRlIjozNjAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0="}]}, {"mode": "managed", "type": "aws_s3_bucket", "name": "terraform_state", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::my-eks-terraform-2025", "bucket": "my-eks-terraform-2025", "bucket_domain_name": "my-eks-terraform-2025.s3.amazonaws.com", "bucket_prefix": "", "bucket_region": "us-east-1", "bucket_regional_domain_name": "my-eks-terraform-2025.s3.us-east-1.amazonaws.com", "cors_rule": [], "force_destroy": false, "grant": [{"id": "651acf2e972d5ae16f6b51cb15a22439283fb7f75c3d5b30a9c253bc0663fc90", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z3AQBSTGFYJSTF", "id": "my-eks-terraform-2025", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "", "region": "us-east-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {"Name": "Terraform state storage"}, "tags_all": {"Name": "Terraform state storage"}, "timeouts": null, "versioning": [{"enabled": false, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19"}]}, {"mode": "managed", "type": "aws_s3_bucket_versioning", "name": "terraform_state", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "my-eks-terraform-2025", "expected_bucket_owner": "", "id": "my-eks-terraform-2025", "mfa": null, "region": "us-east-1", "versioning_configuration": [{"mfa_delete": "", "status": "Enabled"}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.terraform_state"]}]}], "check_results": null}
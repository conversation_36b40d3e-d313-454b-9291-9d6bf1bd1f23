{"Modules": [{"Key": "", "Source": "", "Dir": "."}, {"Key": "eks", "Source": "registry.terraform.io/terraform-aws-modules/eks/aws", "Version": "20.8.5", "Dir": ".terraform/modules/eks"}, {"Key": "eks.eks_managed_node_group", "Source": "./modules/eks-managed-node-group", "Dir": ".terraform/modules/eks/modules/eks-managed-node-group"}, {"Key": "eks.eks_managed_node_group.user_data", "Source": "../_user_data", "Dir": ".terraform/modules/eks/modules/_user_data"}, {"Key": "eks.fargate_profile", "Source": "./modules/fargate-profile", "Dir": ".terraform/modules/eks/modules/fargate-profile"}, {"Key": "eks.kms", "Source": "registry.terraform.io/terraform-aws-modules/kms/aws", "Version": "2.1.0", "Dir": ".terraform/modules/eks.kms"}, {"Key": "eks.self_managed_node_group", "Source": "./modules/self-managed-node-group", "Dir": ".terraform/modules/eks/modules/self-managed-node-group"}, {"Key": "eks.self_managed_node_group.user_data", "Source": "../_user_data", "Dir": ".terraform/modules/eks/modules/_user_data"}, {"Key": "vpc", "Source": "registry.terraform.io/terraform-aws-modules/vpc/aws", "Version": "5.9.0", "Dir": ".terraform/modules/vpc"}]}
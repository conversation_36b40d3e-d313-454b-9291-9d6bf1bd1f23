name: Pre-Commit

on:
  pull_request:
    branches:
      - main
      - master

env:
  TERRAFORM_DOCS_VERSION: v0.16.0
  TFLINT_VERSION: v0.50.3

jobs:
  collectInputs:
    name: Collect workflow inputs
    runs-on: ubuntu-latest
    outputs:
      directories: ${{ steps.dirs.outputs.directories }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Get root directories
        id: dirs
        uses: clowdhaus/terraform-composite-actions/directories@v1.9.0

  preCommitMinVersions:
    name: Min TF pre-commit
    needs: collectInputs
    runs-on: ubuntu-latest
    strategy:
      matrix:
        directory: ${{ fromJson(needs.collectInputs.outputs.directories) }}
    steps:
      # https://github.com/orgs/community/discussions/25678#discussioncomment-5242449
      - name: Delete huge unnecessary tools folder
        run: |
          rm -rf /opt/hostedtoolcache/CodeQL
          rm -rf /opt/hostedtoolcache/Java_Temurin-Hotspot_jdk
          rm -rf /opt/hostedtoolcache/Ruby
          rm -rf /opt/hostedtoolcache/go

      - name: Checkout
        uses: actions/checkout@v4

      - name: Terraform min/max versions
        id: minMax
        uses: clowdhaus/terraform-min-max@v1.3.0
        with:
          directory: ${{ matrix.directory }}

      - name: Pre-commit Terraform ${{ steps.minMax.outputs.minVersion }}
        # Run only validate pre-commit check on min version supported
        if: ${{ matrix.directory !=  '.' }}
        uses: clowdhaus/terraform-composite-actions/pre-commit@v1.9.0
        with:
          terraform-version: ${{ steps.minMax.outputs.minVersion }}
          tflint-version: ${{ env.TFLINT_VERSION }}
          args: 'terraform_validate --color=always --show-diff-on-failure --files ${{ matrix.directory }}/*'

      - name: Pre-commit Terraform ${{ steps.minMax.outputs.minVersion }}
        # Run only validate pre-commit check on min version supported
        if: ${{ matrix.directory ==  '.' }}
        uses: clowdhaus/terraform-composite-actions/pre-commit@v1.9.0
        with:
          terraform-version: ${{ steps.minMax.outputs.minVersion }}
          tflint-version: ${{ env.TFLINT_VERSION }}
          args: 'terraform_validate --color=always --show-diff-on-failure --files $(ls *.tf)'

  preCommitMaxVersion:
    name: Max TF pre-commit
    runs-on: ubuntu-latest
    needs: collectInputs
    steps:
      # https://github.com/orgs/community/discussions/25678#discussioncomment-5242449
      - name: Delete huge unnecessary tools folder
        run: |
          rm -rf /opt/hostedtoolcache/CodeQL
          rm -rf /opt/hostedtoolcache/Java_Temurin-Hotspot_jdk
          rm -rf /opt/hostedtoolcache/Ruby
          rm -rf /opt/hostedtoolcache/go

      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.ref }}
          repository: ${{github.event.pull_request.head.repo.full_name}}

      - name: Terraform min/max versions
        id: minMax
        uses: clowdhaus/terraform-min-max@v1.3.0

      - name: Pre-commit Terraform ${{ steps.minMax.outputs.maxVersion }}
        uses: clowdhaus/terraform-composite-actions/pre-commit@v1.9.0
        with:
          terraform-version: ${{ steps.minMax.outputs.maxVersion }}
          tflint-version: ${{ env.TFLINT_VERSION }}
          terraform-docs-version: ${{ env.TERRAFORM_DOCS_VERSION }}
          install-hcledit: true

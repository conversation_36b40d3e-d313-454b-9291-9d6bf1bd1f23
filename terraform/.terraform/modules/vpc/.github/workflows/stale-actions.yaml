name: 'Mark or close stale issues and PRs'
on:
  schedule:
    - cron: '0 0 * * *'

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          # Staling issues and PR's
          days-before-stale: 30
          stale-issue-label: stale
          stale-pr-label: stale
          stale-issue-message: |
            This issue has been automatically marked as stale because it has been open 30 days
            with no activity. Remove stale label or comment or this issue will be closed in 10 days
          stale-pr-message: |
            This PR has been automatically marked as stale because it has been open 30 days
            with no activity. Remove stale label or comment or this PR will be closed in 10 days
          # Not stale if have this labels or part of milestone
          exempt-issue-labels: bug,wip,on-hold
          exempt-pr-labels: bug,wip,on-hold
          exempt-all-milestones: true
          # Close issue operations
          # Label will be automatically removed if the issues are no longer closed nor locked.
          days-before-close: 10
          delete-branch: true
          close-issue-message: This issue was automatically closed because of stale in 10 days
          close-pr-message: This PR was automatically closed because of stale in 10 days

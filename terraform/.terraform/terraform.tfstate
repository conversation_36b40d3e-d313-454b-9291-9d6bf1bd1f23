{"version": 3, "serial": 1, "lineage": "757b0c3f-1a45-ba89-9b9f-3b825c2f78a0", "backend": {"type": "s3", "config": {"access_key": null, "acl": null, "assume_role_duration_seconds": null, "assume_role_policy": null, "assume_role_policy_arns": null, "assume_role_tags": null, "assume_role_transitive_tag_keys": null, "bucket": "my-eks-terraform-2025", "dynamodb_endpoint": null, "dynamodb_table": "my-eks-terraform-locks", "encrypt": true, "endpoint": null, "external_id": null, "force_path_style": null, "iam_endpoint": null, "key": "eks-cluster/terraform.tfstate", "kms_key_id": null, "max_retries": null, "profile": null, "region": "us-east-1", "role_arn": null, "secret_key": null, "session_name": null, "shared_credentials_file": null, "skip_credentials_validation": null, "skip_metadata_api_check": null, "skip_region_validation": null, "sse_customer_key": null, "sts_endpoint": null, "token": null, "workspace_key_prefix": null}, "hash": 552024664}, "modules": [{"path": ["root"], "outputs": {}, "resources": {}, "depends_on": []}]}
terraform {
  backend "s3" {
    bucket         = "my-eks-terraform-2025"
    key            = "eks-cluster/terraform.tfstate"
    region         = "us-east-1"
    dynamodb_table = "my-eks-terraform-locks"
    encrypt        = true
  }
}

provider "aws" {
  region = "us-east-1"
}

data "aws_eks_cluster" "cluster" {
  name = module.eks.cluster_name
}

data "aws_eks_cluster_auth" "cluster" {
  name = module.eks.cluster_name
}

provider "kubernetes" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority.0.data)
  token                  = data.aws_eks_cluster_auth.cluster.token
}

module "vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "5.9.0"

  name = "eks-vpc"

  cidr = "10.0.0.0/16"
  azs  = ["us-east-1a", "us-east-1b", "us-east-1c"]

  private_subnets = ["********/24", "********/24", "********/24"]
  public_subnets  = ["********/24", "********/24", "********/24"]

  enable_nat_gateway   = true
  single_nat_gateway   = true
  enable_dns_hostnames = true

  public_subnet_tags = {
    "kubernetes.io/cluster/eks-cluster" = "shared"
    "kubernetes.io/role/elb"              = "1"
  }

  private_subnet_tags = {
    "kubernetes.io/cluster/eks-cluster" = "shared"
    "kubernetes.io/role/internal-elb"     = "1"
  }
}

module "eks" {
  source  = "terraform-aws-modules/eks/aws"
  version = "~> 20.0"

  cluster_name    = "eks-cluster"
  cluster_version = "1.28"

  vpc_id     = module.vpc.vpc_id
  subnet_ids = module.vpc.private_subnets

  eks_managed_node_group_defaults = {
    ami_type = "AL2_x86_64"
  }

  eks_managed_node_groups = {
    one = {
      name           = "node-group-1"
      instance_types = ["t2.small"]
      min_size       = 1
      max_size       = 3
      desired_size   = 2

      launch_template_tags = {
        "eks_managed_node_group" = "one"
      }

      block_device_mappings = {
        xvda = {
          device_name = "/dev/xvda"
          ebs = {
            volume_size = 50
            volume_type = "gp3"
            encrypted   = true
          }
        }
      }
    }
  }

  node_security_group_additional_rules = {
    ingress_allow_all_from_self = {
      type        = "ingress"
      from_port   = 0
      to_port     = 0
      protocol    = "-1"
      self        = true
      description = "Allow all traffic from self"
    }
  }
}

resource "aws_ecr_repository" "frontend" {
  name = "frontend"
}

resource "aws_ecr_repository" "backend" {
  name = "backend"
}

data "aws_ecr_repository" "frontend" {
  name = aws_ecr_repository.frontend.name
}

data "aws_ecr_repository" "backend" {
  name = aws_ecr_repository.backend.name
}

locals {
  backend_manifests = split("---", replace(file("${path.module}/kubernetes/backend.yml"), "$ECR_REGISTRY", data.aws_ecr_repository.backend.repository_url))
  frontend_manifests = split("---", replace(file("${path.module}/kubernetes/frontend.yml"), "$ECR_REGISTRY", data.aws_ecr_repository.frontend.repository_url))
}

resource "kubernetes_manifest" "backend_deployment" {
  manifest = yamldecode(trim(local.backend_manifests[0], " \n"))

  depends_on = [
    module.eks.cluster_endpoint,
    module.eks.cluster_certificate_authority_data,
  ]
}

resource "kubernetes_manifest" "backend_service" {
  manifest = yamldecode(trim(local.backend_manifests[1], " \n"))

  depends_on = [
    module.eks.cluster_endpoint,
    module.eks.cluster_certificate_authority_data,
  ]
}

resource "kubernetes_manifest" "frontend_deployment" {
  manifest = yamldecode(trim(local.frontend_manifests[0], " \n"))

  depends_on = [
    module.eks.cluster_endpoint,
    module.eks.cluster_certificate_authority_data,
  ]
}

resource "kubernetes_manifest" "frontend_service" {
  manifest = yamldecode(trim(local.frontend_manifests[1], " \n"))

  depends_on = [
    module.eks.cluster_endpoint,
    module.eks.cluster_certificate_authority_data,
  ]
}

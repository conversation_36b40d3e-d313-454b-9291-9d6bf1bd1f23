# Use an official Node.js runtime as a parent image
FROM node:16-alpine

# Set the working directory in the container
WORKDIR /app

# Copy package.json and package-lock.json to the container
COPY package*.json ./

# Install application dependencies
RUN npm install

# Copy the rest of the application's code into the container
COPY . .

# Build the React application for production
RUN npm run build

# Expose port 3000 for the application
EXPOSE 3000

# Serve the built React application
CMD ["npm", "start"]
